{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚀 Autonomous Trading System Training Notebook\n", "\n", "This notebook provides an interactive interface for training and analyzing autonomous trading agents with advanced visualization capabilities.\n", "\n", "## Features:\n", "- 🧠 **Autonomous Agent Training**: Self-evolving neural architectures with hyperparameter tuning\n", "- 📊 **Real-time Visualization**: Training progress, fitness evolution, and performance metrics\n", "- 🔄 **Complete Training Pipeline**: PPO → MoE → MAML → Autonomous\n", "- 💰 **Risk Management**: Integrated 1:2 risk-reward ratio with ATR-based stops\n", "- 🎯 **Champion Selection**: Automatic saving of best-performing agents\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Setup and Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'MultiHeadTransformerModel' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 31\u001b[39m\n\u001b[32m     28\u001b[39m sys.path.append(os.path.dirname(os.path.abspath(\u001b[33m'\u001b[39m\u001b[33m.\u001b[39m\u001b[33m'\u001b[39m)))\n\u001b[32m     30\u001b[39m \u001b[38;5;66;03m# Import trading system components\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m31\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtraining\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01<PERSON><PERSON><PERSON><PERSON>_trainer\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ParallelTrainer\n\u001b[32m     32\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtraining\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mparallel_config\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ParallelTrainingConfig, get_recommended_config\n\u001b[32m     33\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtraining\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainer\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Trainer\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\AlgoTrading\\src\\training\\parallel_trainer.py:20\u001b[39m\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata_loader\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m DataLoader\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01magents\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mbase_agent\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m BaseAgent\n\u001b[32m---> \u001b[39m\u001b[32m20\u001b[39m \u001b[38;5;28;01mf<PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01magents\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmoe_agent\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m MoEAgent\n\u001b[32m     21\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mhardware_optimizer\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m get_hardware_optimizer\n\u001b[32m     23\u001b[39m torch, nn = try_import_torch()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\AlgoTrading\\src\\agents\\moe_agent.py:9\u001b[39m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtyping\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m <PERSON><PERSON>, <PERSON>, Dict\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01magents\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mbase_agent\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m BaseAgent\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodels\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtransformer_models\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m TransformerModel, ActorTransformerModel\n\u001b[32m     11\u001b[39m \u001b[38;5;28;01mclass\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mGatingNetwork\u001b[39;00m(nn.Module):\n\u001b[32m     12\u001b[39m     \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, input_dim: \u001b[38;5;28mint\u001b[39m, num_experts: \u001b[38;5;28mint\u001b[39m, hidden_dim: \u001b[38;5;28mint\u001b[39m):\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\AlgoTrading\\src\\models\\transformer_models.py:89\u001b[39m\n\u001b[32m     85\u001b[39m             x = x.unsqueeze(\u001b[32m1\u001b[39m)\n\u001b[32m     86\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.transformer.get_attention_weights(x, mask=mask)\n\u001b[32m---> \u001b[39m\u001b[32m89\u001b[39m \u001b[38;5;28;01mclass\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mActorTransformerModel\u001b[39;00m(\u001b[43mMultiHeadTransformerModel\u001b[49m):\n\u001b[32m     90\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m     91\u001b[39m \u001b[33;03m    Actor model using Transformer architecture with multi-head output.\u001b[39;00m\n\u001b[32m     92\u001b[39m \u001b[33;03m    \u001b[39;00m\n\u001b[32m     93\u001b[39m \u001b[33;03m    This model outputs both discrete action probabilities and a continuous quantity.\u001b[39;00m\n\u001b[32m     94\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m     96\u001b[39m     \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__init__\u001b[39m(\n\u001b[32m     97\u001b[39m         \u001b[38;5;28mself\u001b[39m, \n\u001b[32m     98\u001b[39m         input_dim: \u001b[38;5;28mint\u001b[39m, \n\u001b[32m   (...)\u001b[39m\u001b[32m    105\u001b[39m         max_seq_len: \u001b[38;5;28mint\u001b[39m = \u001b[32m1000\u001b[39m\n\u001b[32m    106\u001b[39m     ):\n", "\u001b[31mNameError\u001b[39m: name 'MultiHeadTransformerModel' is not defined"]}], "source": ["#!/usr/bin/env python3\n", "\"\"\"\n", "Autonomous Trading System Training Notebook\n", "Interactive training and analysis for self-evolving trading agents\n", "\"\"\"\n", "\n", "import os\n", "import sys\n", "import logging\n", "import warnings\n", "from typing import List, Dict, Any\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import plotly.express as px\n", "from IPython.display import display, HTML, clear_output\n", "import ipywidgets as widgets\n", "from tqdm.notebook import tqdm\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add project root to path\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Import trading system components\n", "from src.training.parallel_trainer import ParallelTrainer\n", "from src.training.parallel_config import ParallelTrainingConfig, get_recommended_config\n", "from src.training.trainer import Trainer\n", "from src.training.sequence_manager import TrainingSequenceManager\n", "from src.training.autonomous_trainer import AutonomousTrainer, AutonomousTrainingConfig, run_autonomous_stage\n", "from src.agents.ppo_agent import PPOAgent\n", "from src.agents.moe_agent import MoEAgent\n", "from src.agents.autonomous_agent import AutonomousAgent\n", "from src.backtesting.environment import TradingEnv\n", "from src.utils.data_loader import DataLoader\n", "from src.config.config import INITIAL_CAPITAL\n", "\n", "# Configure logging for notebook\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(levelname)s - %(message)s'\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"🚀 Autonomous Trading System Initialized!\")\n", "print(f\"💰 Initial Capital: ₹{INITIAL_CAPITAL:,.2f}\")\n", "print(f\"📅 Session Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Configuration and Data Discovery"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Found 3 available symbols:\n", "   1. Bank_Nifty_5\n", "   2. Nifty_2\n", "   3. Sensex_15\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a97b1380d5a3436fa566cea7c6091f12", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(HTML(value='<h3>🎛️ Training Configuration</h3>'), SelectMultiple(description='Symbols:', index=…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def get_available_symbols(data_dir: str = \"data/final\") -> List[str]:\n", "    \"\"\"Get list of available trading symbols from data directory.\"\"\"\n", "    symbols = []\n", "    \n", "    if not os.path.exists(data_dir):\n", "        logger.warning(f\"Data directory {data_dir} does not exist\")\n", "        return symbols\n", "\n", "    for filename in os.listdir(data_dir):\n", "        if filename.endswith('.csv') and filename.startswith('features_'):\n", "            symbol = filename.replace('features_', '').replace('.csv', '')\n", "            symbols.append(symbol)\n", "    \n", "    return sorted(symbols)\n", "\n", "# Discover available data\n", "DATA_DIR = \"data/final\"\n", "available_symbols = get_available_symbols(DATA_DIR)\n", "\n", "print(f\"📊 Found {len(available_symbols)} available symbols:\")\n", "for i, symbol in enumerate(available_symbols, 1):\n", "    print(f\"  {i:2d}. {symbol}\")\n", "\n", "# Create interactive symbol selector\n", "symbol_selector = widgets.SelectMultiple(\n", "    options=available_symbols,\n", "    value=[available_symbols[0]] if available_symbols else [],\n", "    description='Symbols:',\n", "    disabled=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "algorithm_selector = widgets.Dropdown(\n", "    options=['Autonomous', 'PPO', 'MoE', 'MAML'],\n", "    value='Autonomous',\n", "    description='Algorithm:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "training_mode = widgets.Dropdown(\n", "    options=['sequence', 'simple', 'parallel'],\n", "    value='sequence',\n", "    description='Mode:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "episodes_slider = widgets.IntSlider(\n", "    value=50,\n", "    min=1,\n", "    max=1000,\n", "    step=10,\n", "    description='Episodes/Generations:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "display(widgets.VBox([\n", "    widgets.HTML(\"<h3>🎛️ Training Configuration</h3>\"),\n", "    symbol_selector,\n", "    algorithm_selector,\n", "    training_mode,\n", "    episodes_slider\n", "]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 Data Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_symbol_data(symbol: str, data_dir: str = \"data/final\") -> Dict[str, Any]:\n", "    \"\"\"Analyze and visualize symbol data.\"\"\"\n", "    try:\n", "        # Load data\n", "        data_path = os.path.join(data_dir, f\"features_{symbol}.csv\")\n", "        df = pd.read_csv(data_path)\n", "        \n", "        # Basic statistics\n", "        stats = {\n", "            'symbol': symbol,\n", "            'total_rows': len(df),\n", "            'date_range': (df['timestamp'].min(), df['timestamp'].max()) if 'timestamp' in df.columns else ('N/A', 'N/A'),\n", "            'features': len(df.columns),\n", "            'missing_values': df.isnull().sum().sum(),\n", "            'price_columns': [col for col in df.columns if any(x in col.lower() for x in ['price', 'close', 'open', 'high', 'low'])]\n", "        }\n", "        \n", "        return stats, df\n", "    except Exception as e:\n", "        logger.error(f\"Error analyzing {symbol}: {e}\")\n", "        return None, None\n", "\n", "def plot_symbol_overview(symbol: str, df: pd.DataFrame):\n", "    \"\"\"Create comprehensive visualization of symbol data.\"\"\"\n", "    fig = make_subplots(\n", "        rows=2, cols=2,\n", "        subplot_titles=(\n", "            f'{symbol} Price Action',\n", "            'Feature Distribution',\n", "            'Volume Analysis',\n", "            'Volatility Patterns'\n", "        ),\n", "        specs=[[{\"secondary_y\": True}, {}],\n", "               [{}, {}]]\n", "    )\n", "    \n", "    # Price action (if available)\n", "    price_cols = [col for col in df.columns if any(x in col.lower() for x in ['close', 'price'])]\n", "    if price_cols:\n", "        price_col = price_cols[0]\n", "        fig.add_trace(\n", "            go.<PERSON>er(x=df.index, y=df[price_col], name='Price', line=dict(color='blue')),\n", "            row=1, col=1\n", "        )\n", "    \n", "    # Feature distribution\n", "    numeric_cols = df.select_dtypes(include=[np.number]).columns[:10]  # Top 10 numeric features\n", "    for col in numeric_cols:\n", "        fig.add_trace(\n", "            go.Histogram(x=df[col], name=col, opacity=0.7),\n", "            row=1, col=2\n", "        )\n", "    \n", "    # Volume analysis (if available)\n", "    volume_cols = [col for col in df.columns if 'volume' in col.lower()]\n", "    if volume_cols:\n", "        volume_col = volume_cols[0]\n", "        fig.add_trace(\n", "            go.Bar(x=df.index[-100:], y=df[volume_col].iloc[-100:], name='Volume'),\n", "            row=2, col=1\n", "        )\n", "    \n", "    # Volatility patterns\n", "    if price_cols:\n", "        returns = df[price_cols[0]].pct_change().dropna()\n", "        rolling_vol = returns.rolling(window=20).std() * np.sqrt(252)  # Annualized volatility\n", "        fig.add_trace(\n", "            go.Scatter(x=rolling_vol.index, y=rolling_vol, name='Rolling Volatility'),\n", "            row=2, col=2\n", "        )\n", "    \n", "    fig.update_layout(\n", "        height=800,\n", "        title_text=f\"📊 {symbol} Data Analysis Overview\",\n", "        showlegend=True\n", "    )\n", "    \n", "    return fig\n", "\n", "# Analyze selected symbol\n", "if available_symbols:\n", "    selected_symbol = available_symbols[0]  # Default to first symbol\n", "    stats, df = analyze_symbol_data(selected_symbol)\n", "    \n", "    if stats and df is not None:\n", "        print(f\"\\n📊 Analysis for {selected_symbol}:\")\n", "        print(f\"  📈 Total Data Points: {stats['total_rows']:,}\")\n", "        print(f\"  🔢 Features: {stats['features']}\")\n", "        print(f\"  📅 Date Range: {stats['date_range'][0]} to {stats['date_range'][1]}\")\n", "        print(f\"  ❌ Missing Values: {stats['missing_values']}\")\n", "        \n", "        # Create and display visualization\n", "        fig = plot_symbol_overview(selected_symbol, df)\n", "        fig.show()\n", "    else:\n", "        print(f\"❌ Could not analyze {selected_symbol}\")\n", "else:\n", "    print(\"❌ No symbols available for analysis\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}