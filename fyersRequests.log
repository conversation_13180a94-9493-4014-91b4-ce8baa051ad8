{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 11:57:32,128+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 11:57:54,874+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 11:58:21,988+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 11:58:47,743+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 11:59:14,388+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 11:59:55,267+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:00:30,639+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:02:48,674+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:03:25,578+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:03:48,403+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:05:09,424+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:06:41,872+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:06:58,987+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:07:26,853+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:07:43,626+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:08:08,927+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:08:25,947+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:09:27,282+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:10:28,652+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:11:29,958+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:12:09,754+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:12:42,484+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:14:36,003+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:14:57,797+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:15:59,000+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:17:00,086+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:18:01,293+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:18:45,778+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:19:04,469+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-18 12:24:55,263+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-18 12:25:11,705+0530","service":"FyersAPIRequest"}
