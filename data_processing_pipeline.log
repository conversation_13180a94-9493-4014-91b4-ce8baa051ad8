2025-07-18 23:23:50,544 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-18 23:23:50,544 - __main__ - INFO - ================================================================================
2025-07-18 23:23:50,544 - __main__ - INFO - STARTING COMPLETE DATA PROCESSING PIPELINE
2025-07-18 23:23:50,544 - __main__ - INFO - ================================================================================
2025-07-18 23:23:50,544 - __main__ - INFO - STEP 1: FEATURE GENERATION
2025-07-18 23:23:50,544 - __main__ - INFO - ----------------------------------------
2025-07-18 23:23:50,544 - __main__ - INFO - Running feature generator...
2025-07-18 23:23:50,919 - feature_generator - INFO - Found 2 CSV files to process
2025-07-18 23:23:51,013 - feature_generator - INFO - Loaded 999 rows from Bank_Nifty_5.csv
2025-07-18 23:23:51,013 - feature_generator - INFO - Processing in-memory DataFrame with 999 rows...
2025-07-18 23:23:51,826 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 800 rows
2025-07-18 23:23:51,842 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-18 23:23:51,888 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 800 rows, 63 features
2025-07-18 23:23:51,888 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-18 23:23:51,904 - feature_generator - INFO - Loaded 2499 rows from Nifty_2.csv
2025-07-18 23:23:51,904 - feature_generator - INFO - Processing in-memory DataFrame with 2499 rows...
2025-07-18 23:23:53,998 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2300 rows
2025-07-18 23:23:53,998 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-18 23:23:54,138 - feature_generator - INFO - Processed Nifty_2.csv: 2300 rows, 63 features
2025-07-18 23:23:54,138 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-18 23:23:54,138 - __main__ - INFO - Feature generator completed successfully
2025-07-18 23:23:54,138 - __main__ - INFO - Feature generation completed: 0 files
2025-07-18 23:23:54,154 - __main__ - INFO - Pipeline report saved: reports\pipeline\pipeline_report_1752861234.txt
2025-07-18 23:23:54,154 - __main__ - INFO - ================================================================================
2025-07-18 23:23:54,154 - __main__ - INFO - PIPELINE COMPLETED SUCCESSFULLY
2025-07-18 23:23:54,154 - __main__ - INFO - ================================================================================
2025-07-18 23:25:57,041 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-18 23:25:57,056 - __main__ - INFO - ================================================================================
2025-07-18 23:25:57,056 - __main__ - INFO - STARTING COMPLETE DATA PROCESSING PIPELINE
2025-07-18 23:25:57,056 - __main__ - INFO - ================================================================================
2025-07-18 23:25:57,056 - __main__ - INFO - STEP 1: FEATURE GENERATION
2025-07-18 23:25:57,056 - __main__ - INFO - ----------------------------------------
2025-07-18 23:25:57,056 - __main__ - INFO - Running feature generator...
2025-07-18 23:25:57,434 - feature_generator - INFO - Found 2 CSV files to process
2025-07-18 23:25:57,454 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-18 23:25:57,539 - feature_generator - INFO - Loaded 999 rows from Bank_Nifty_5.csv
2025-07-18 23:25:57,539 - feature_generator - INFO - Processing in-memory DataFrame with 999 rows...
2025-07-18 23:25:58,582 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 800 rows
2025-07-18 23:25:58,582 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-18 23:25:58,647 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 800 rows, 62 features
2025-07-18 23:25:58,647 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-18 23:25:58,647 - feature_generator - INFO - Dropped 'volume' column from Nifty_2.csv
2025-07-18 23:25:58,664 - feature_generator - INFO - Loaded 2499 rows from Nifty_2.csv
2025-07-18 23:25:58,664 - feature_generator - INFO - Processing in-memory DataFrame with 2499 rows...
2025-07-18 23:26:00,795 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2300 rows
2025-07-18 23:26:00,795 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-18 23:26:00,949 - feature_generator - INFO - Processed Nifty_2.csv: 2300 rows, 62 features
2025-07-18 23:26:00,950 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-18 23:26:00,950 - __main__ - INFO - Feature generator completed successfully
2025-07-18 23:26:00,951 - __main__ - INFO - Feature generation completed: 0 files
2025-07-18 23:26:00,953 - __main__ - INFO - Pipeline report saved: reports\pipeline\pipeline_report_1752861360.txt
2025-07-18 23:26:00,953 - __main__ - INFO - ================================================================================
2025-07-18 23:26:00,953 - __main__ - INFO - PIPELINE COMPLETED SUCCESSFULLY
2025-07-18 23:26:00,954 - __main__ - INFO - ================================================================================
2025-07-20 23:29:23,652 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-20 23:29:23,652 - __main__ - INFO - ================================================================================
2025-07-20 23:29:23,652 - __main__ - INFO - STARTING COMPLETE DATA PROCESSING PIPELINE
2025-07-20 23:29:23,652 - __main__ - INFO - ================================================================================
2025-07-20 23:29:23,652 - __main__ - INFO - STEP 1: FEATURE GENERATION
2025-07-20 23:29:23,652 - __main__ - INFO - ----------------------------------------
2025-07-20 23:29:23,652 - __main__ - INFO - Running feature generator...
2025-07-20 23:29:25,803 - feature_generator - INFO - Found 2 CSV files to process
2025-07-20 23:29:25,843 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-20 23:29:29,118 - feature_generator - INFO - Loaded 999 rows from Bank_Nifty_5.csv
2025-07-20 23:29:29,118 - feature_generator - INFO - Processing in-memory DataFrame with 999 rows...
2025-07-20 23:29:29,974 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 800 rows
2025-07-20 23:29:29,974 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-20 23:29:30,042 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 800 rows, 62 features
2025-07-20 23:29:30,043 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-20 23:29:30,062 - feature_generator - INFO - Dropped 'volume' column from Nifty_2.csv
2025-07-20 23:29:30,066 - feature_generator - INFO - Loaded 2499 rows from Nifty_2.csv
2025-07-20 23:29:30,067 - feature_generator - INFO - Processing in-memory DataFrame with 2499 rows...
2025-07-20 23:29:32,320 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2300 rows
2025-07-20 23:29:32,320 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-20 23:29:32,467 - feature_generator - INFO - Processed Nifty_2.csv: 2300 rows, 62 features
2025-07-20 23:29:32,467 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-20 23:29:32,467 - __main__ - INFO - Feature generator completed successfully
2025-07-20 23:29:32,468 - __main__ - INFO - Feature generation completed: 0 files
2025-07-20 23:29:32,469 - __main__ - INFO - Pipeline report saved: reports\pipeline\pipeline_report_1753034372.txt
2025-07-20 23:29:32,470 - __main__ - INFO - ================================================================================
2025-07-20 23:29:32,470 - __main__ - INFO - PIPELINE COMPLETED SUCCESSFULLY
2025-07-20 23:29:32,470 - __main__ - INFO - ================================================================================
2025-07-20 23:30:48,187 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-20 23:30:48,187 - __main__ - INFO - ================================================================================
2025-07-20 23:30:48,187 - __main__ - INFO - STARTING COMPLETE DATA PROCESSING PIPELINE
2025-07-20 23:30:48,187 - __main__ - INFO - ================================================================================
2025-07-20 23:30:48,187 - __main__ - INFO - STEP 1: FEATURE GENERATION
2025-07-20 23:30:48,187 - __main__ - INFO - ----------------------------------------
2025-07-20 23:30:48,187 - __main__ - INFO - Running feature generator...
2025-07-20 23:30:48,513 - feature_generator - INFO - Found 3 CSV files to process
2025-07-20 23:30:48,565 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-20 23:30:48,657 - feature_generator - INFO - Loaded 19983 rows from Bank_Nifty_5.csv
2025-07-20 23:30:48,657 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-20 23:31:03,276 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-20 23:31:03,276 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-20 23:31:04,561 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 19784 rows, 62 features
2025-07-20 23:31:04,561 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-20 23:31:04,644 - feature_generator - INFO - Dropped 'volume' column from Nifty_2.csv
2025-07-20 23:31:04,676 - feature_generator - INFO - Loaded 50093 rows from Nifty_2.csv
2025-07-20 23:31:04,677 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-20 23:31:42,785 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-20 23:31:42,785 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-20 23:31:45,577 - feature_generator - INFO - Processed Nifty_2.csv: 49894 rows, 62 features
2025-07-20 23:31:45,577 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-20 23:31:45,593 - feature_generator - INFO - Dropped 'volume' column from Sensex_15.csv
2025-07-20 23:31:45,612 - feature_generator - INFO - Loaded 6661 rows from Sensex_15.csv
2025-07-20 23:31:45,612 - feature_generator - INFO - Processing in-memory DataFrame with 6661 rows...
2025-07-20 23:31:51,356 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 6462 rows
2025-07-20 23:31:51,356 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-20 23:31:51,810 - feature_generator - INFO - Processed Sensex_15.csv: 6462 rows, 62 features
2025-07-20 23:31:51,810 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_15.csv
2025-07-20 23:31:51,810 - __main__ - INFO - Feature generator completed successfully
2025-07-20 23:31:51,810 - __main__ - INFO - Feature generation completed: 0 files
2025-07-20 23:31:51,810 - __main__ - INFO - Pipeline report saved: reports\pipeline\pipeline_report_1753034511.txt
2025-07-20 23:31:51,810 - __main__ - INFO - ================================================================================
2025-07-20 23:31:51,810 - __main__ - INFO - PIPELINE COMPLETED SUCCESSFULLY
2025-07-20 23:31:51,810 - __main__ - INFO - ================================================================================
